#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>

// 优化的随机数生成器结构体
typedef struct {
    uint32_t increase_count;
    uint32_t decrease_count;
    uint32_t total_count;
} RandomStats;

// 全局统计变量
static RandomStats g_stats = {0, 0, 0};

/**
 * 原始扰动函数
 * @param ms 毫秒时间戳
 * @return 扰动后的随机数
 */
uint16_t disturb(uint16_t ms) {
    uint16_t random = ms & 0xFFFF;
    random ^= (random << 3) & 0xFFFF;
    random ^= (random >> 5) & 0xFFFF;
    random ^= (random << 2) & 0xFFFF;
    return random & 0xFFFF;
}

/**
 * 基于游戏时间的优化随机数生成器
 * @param ms 毫秒时间戳
 * @param game_time 当前游戏时间 (int16_t)
 * @param game_time_last 上次游戏时间 (int16_t)
 * @param is_increase 输出参数：是否为增加操作 (1=增加, 0=减少)
 * @return 最终随机值
 */
uint16_t generate_random_with_gametime(uint16_t ms, int16_t game_time, int16_t game_time_last, uint8_t *is_increase) {
    // 生成基础随机数
    uint16_t random = disturb(ms);
    
    // 计算游戏时间相关参数
    uint16_t abs_game_time_last = (uint16_t)abs(game_time_last);
    uint16_t game_time_acc = (uint16_t)(game_time + abs_game_time_last);
    
    // 映射随机数到游戏时间累积范围
    uint16_t map_game_time_acc;
    if (game_time_acc == 0) {
        map_game_time_acc = 0;
    } else {
        // 使用定点数运算避免浮点运算
        map_game_time_acc = (uint16_t)(((uint32_t)random * game_time_acc) / 0xFFFF);
    }
    
    uint16_t final_value;
    
    // 判断增加还是减少
    if (map_game_time_acc >= abs_game_time_last) {
        // 增加值
        final_value = map_game_time_acc - abs_game_time_last;
        *is_increase = 1;
        g_stats.increase_count++;
    } else {
        // 减少值
        final_value = abs_game_time_last - map_game_time_acc;
        *is_increase = 0;
        g_stats.decrease_count++;
    }
    
    g_stats.total_count++;
    
    return final_value;
}

/**
 * 获取增加概率 (返回值需要除以1000得到实际概率)
 * @return 增加概率 * 1000
 */
uint16_t get_increase_probability_x1000(void) {
    if (g_stats.total_count == 0) return 0;
    return (uint16_t)(((uint32_t)g_stats.increase_count * 1000) / g_stats.total_count);
}

/**
 * 获取减少概率 (返回值需要除以1000得到实际概率)
 * @return 减少概率 * 1000
 */
uint16_t get_decrease_probability_x1000(void) {
    if (g_stats.total_count == 0) return 0;
    return (uint16_t)(((uint32_t)g_stats.decrease_count * 1000) / g_stats.total_count);
}

/**
 * 获取统计信息
 * @param stats 输出统计信息的指针
 */
void get_random_stats(RandomStats *stats) {
    *stats = g_stats;
}

/**
 * 重置统计信息
 */
void reset_random_stats(void) {
    g_stats.increase_count = 0;
    g_stats.decrease_count = 0;
    g_stats.total_count = 0;
}

/**
 * 打印统计信息
 */
void print_random_stats(void) {
    printf("Random Generator Statistics:\n");
    printf("Total count: %u\n", g_stats.total_count);
    printf("Increase count: %u\n", g_stats.increase_count);
    printf("Decrease count: %u\n", g_stats.decrease_count);
    if (g_stats.total_count > 0) {
        printf("Increase probability: %u.%03u%%\n", 
               get_increase_probability_x1000() / 10,
               get_increase_probability_x1000() % 10);
        printf("Decrease probability: %u.%03u%%\n", 
               get_decrease_probability_x1000() / 10,
               get_decrease_probability_x1000() % 10);
    }
}

// 测试函数
#ifdef TEST_RANDOM_GENERATOR
int main(void) {
    printf("=== Optimized Random Generator Test ===\n\n");
    
    // 测试数据
    struct {
        uint16_t ms;
        int16_t game_time;
        int16_t game_time_last;
    } test_cases[] = {
        {1000, 100, -50},
        {2000, -200, 150},
        {3000, 0, 0},
        {4000, 32767, -32768},
        {5000, -1, 1},
        {16287, 31768, -14759},
        {11721, -16920, -550},
        {42141, 15752, -32016},
        {49064, 7191, -29406},
        {17714, 17929, -6967}
    };
    
    printf("%-6s %-8s %-10s %-10s %-8s %-8s %-6s %-12s %-12s\n",
           "No.", "ms", "GameTime", "LastTime", "Random", "Result", "Op", "Inc.Prob", "Dec.Prob");
    printf("--------------------------------------------------------------------------------\n");
    
    for (int i = 0; i < sizeof(test_cases) / sizeof(test_cases[0]); i++) {
        uint8_t is_increase;
        uint16_t result = generate_random_with_gametime(
            test_cases[i].ms,
            test_cases[i].game_time,
            test_cases[i].game_time_last,
            &is_increase
        );
        
        uint16_t original_random = disturb(test_cases[i].ms);
        uint16_t inc_prob = get_increase_probability_x1000();
        uint16_t dec_prob = get_decrease_probability_x1000();
        
        printf("%-6d %-8u %-10d %-10d %-8u %-8u %-6s %u.%03u%%     %u.%03u%%\n",
               i + 1,
               test_cases[i].ms,
               test_cases[i].game_time,
               test_cases[i].game_time_last,
               original_random,
               result,
               is_increase ? "Inc" : "Dec",
               inc_prob / 10, inc_prob % 10,
               dec_prob / 10, dec_prob % 10
        );
    }
    
    printf("\n");
    print_random_stats();
    
    return 0;
}
#endif
