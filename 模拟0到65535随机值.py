# 模拟 ms 从 0 到 65535 的每一个值，观察扰动后的 r 的分布情况

import matplotlib.pyplot as plt
import numpy as np

def disturb(ms):
    r = ms & 0xFFFF
    r ^= (r << 3) & 0xFFFF
    r ^= (r >> 5) & 0xFFFF
    r ^= (r << 2) & 0xFFFF
    return r & 0xFFFF

# 模拟范围：由于 65536 个点太多，我们可以画分段样本，比如每隔 64 取一个点，方便可视化
samples = np.arange(0, 65536, 64)
results = [disturb(ms) for ms in samples]

# 绘图
plt.figure(figsize=(12, 6))
plt.plot(samples, results, '.', markersize=2, label="Disturbed r")
plt.xlabel("ms input (0 ~ 65535)")
plt.ylabel("r after disturbance")
plt.title("Disturbance Result of r = ms ^ (r<<3) ^ (r>>5) ^ (r<<2)")
plt.grid(True)
plt.legend()
plt.tight_layout()
plt.show()
# 显示最大值
max_r = max(results)
max_ms = samples[results.index(max_r)]
plt.annotate(f'Max: ({max_ms}, {max_r})', 
            xy=(max_ms, max_r),
            xytext=(10, 10),
            textcoords='offset points',
            ha='left',
            va='bottom',
            bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.5),
            arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
plt.show()





