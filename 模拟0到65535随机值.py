# 优化的随机数生成器，基于游戏时间的随机值生成和统计

import matplotlib.pyplot as plt
import numpy as np

def disturb(ms):
    """原始的扰动函数"""
    random = ms & 0xFFFF
    random ^= (random << 3) & 0xFFFF
    random ^= (random >> 5) & 0xFFFF
    random ^= (random << 2) & 0xFFFF
    return random & 0xFFFF

class GameTimeRandomizer:
    def __init__(self):
        self.increase_count = 0
        self.decrease_count = 0
        self.total_count = 0
        self.history = []

    def generate_random_with_gametime(self, ms, game_time, game_time_last):
        """
        基于游戏时间生成随机数

        Args:
            ms: 毫秒时间戳
            game_time: int16_t 当前游戏时间
            game_time_last: int16_t 上次游戏时间

        Returns:
            tuple: (最终随机值, 是否增加, 增加概率, 减少概率)
        """
        # 生成基础随机数
        random = disturb(ms)

        # 计算游戏时间相关参数
        abs_game_time_last = abs(game_time_last) & 0xFFFF
        game_time_acc = (game_time + abs_game_time_last) & 0xFFFF

        # 映射随机数到游戏时间累积范围
        if game_time_acc == 0:
            map_game_time_acc = 0
        else:
            map_game_time_acc = int((random / 0xFFFF) * game_time_acc)

        # 判断增加还是减少
        is_increase = False
        final_value = 0

        if map_game_time_acc >= abs_game_time_last:
            # 增加值
            final_value = map_game_time_acc - abs_game_time_last
            is_increase = True
            self.increase_count += 1
        else:
            # 减少值
            final_value = abs_game_time_last - map_game_time_acc
            is_increase = False
            self.decrease_count += 1

        self.total_count += 1

        # 记录历史
        self.history.append({
            'ms': ms,
            'game_time': game_time,
            'game_time_last': game_time_last,
            'random': random,
            'final_value': final_value,
            'is_increase': is_increase
        })

        # 计算概率
        increase_prob = self.increase_count / self.total_count if self.total_count > 0 else 0
        decrease_prob = self.decrease_count / self.total_count if self.total_count > 0 else 0

        return final_value, is_increase, increase_prob, decrease_prob

    def get_statistics(self):
        """获取统计信息"""
        if self.total_count == 0:
            return {
                'total_count': 0,
                'increase_count': 0,
                'decrease_count': 0,
                'increase_probability': 0,
                'decrease_probability': 0
            }

        return {
            'total_count': self.total_count,
            'increase_count': self.increase_count,
            'decrease_count': self.decrease_count,
            'increase_probability': self.increase_count / self.total_count,
            'decrease_probability': self.decrease_count / self.total_count
        }

    def reset_statistics(self):
        """重置统计信息"""
        self.increase_count = 0
        self.decrease_count = 0
        self.total_count = 0
        self.history = []

# 测试和可视化
def test_game_time_randomizer():
    randomizer = GameTimeRandomizer()

    # 模拟测试数据
    test_samples = 1000
    ms_values = np.random.randint(0, 65536, test_samples)
    game_times = np.random.randint(-32768, 32767, test_samples)
    game_times_last = np.random.randint(-32768, 32767, test_samples)

    results = []
    increase_probs = []
    decrease_probs = []

    print("开始测试游戏时间随机数生成器...")
    print(f"{'序号':<6} {'ms':<8} {'GameTime':<10} {'LastTime':<10} {'随机值':<8} {'结果':<8} {'操作':<6} {'增加概率':<10} {'减少概率':<10}")
    print("-" * 80)

    for i in range(min(20, test_samples)):  # 只显示前20个结果
        ms = ms_values[i]
        gt = game_times[i]
        gtl = game_times_last[i]

        final_val, is_inc, inc_prob, dec_prob = randomizer.generate_random_with_gametime(ms, gt, gtl)

        results.append(final_val)
        increase_probs.append(inc_prob)
        decrease_probs.append(dec_prob)

        operation = "增加" if is_inc else "减少"
        print(f"{i+1:<6} {ms:<8} {gt:<10} {gtl:<10} {disturb(ms):<8} {final_val:<8} {operation:<6} {inc_prob:<10.3f} {dec_prob:<10.3f}")

    # 处理剩余的样本（不显示）
    for i in range(20, test_samples):
        ms = ms_values[i]
        gt = game_times[i]
        gtl = game_times_last[i]

        final_val, is_inc, inc_prob, dec_prob = randomizer.generate_random_with_gametime(ms, gt, gtl)
        results.append(final_val)
        increase_probs.append(inc_prob)
        decrease_probs.append(dec_prob)

    # 显示最终统计
    stats = randomizer.get_statistics()
    print(f"\n最终统计结果:")
    print(f"总次数: {stats['total_count']}")
    print(f"增加次数: {stats['increase_count']}")
    print(f"减少次数: {stats['decrease_count']}")
    print(f"增加概率: {stats['increase_probability']:.3f}")
    print(f"减少概率: {stats['decrease_probability']:.3f}")

    return results, increase_probs, decrease_probs, randomizer

# 运行测试
if __name__ == "__main__":
    results, inc_probs, dec_probs, randomizer = test_game_time_randomizer()





